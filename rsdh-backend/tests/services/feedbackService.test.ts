import { describe, it, expect, beforeAll, afterAll, beforeEach } from 'vitest';
import { prisma } from '../../src/lib/prisma';
import { FeedbackService } from '../../src/services/feedbackService';
import { TestUtils } from '../helpers/testUtils';

describe('FeedbackService', () => {
  let testUser: any;
  let testAdmin: any;
  let testTutor: any;
  let testAppointment: any;
  let testFile: any;

  beforeAll(async () => {
    // Create test users
    testUser = await TestUtils.createTestUser('<EMAIL>', 'Feedback User');
    testAdmin = await TestUtils.createTestUser('<EMAIL>', 'Feedback Admin');

    // Update admin role
    await prisma.user.update({
      where: { id: testAdmin.id },
      data: { role: 'admin' }
    });

    // Create test tutor
    testTutor = await TestUtils.createTestTutor(testAdmin.id, {
      title: 'Test Tutor for Feedback'
    });

    // Create test appointment
    testAppointment = await TestUtils.createTestAppointment(testTutor.id, testUser.id, {
      status: 'completed'
    });

    // Create test file
    testFile = await prisma.file.create({
      data: {
        originalName: 'feedback-attachment.jpg',
        fileName: 'feedback-attachment-123.jpg',
        mimeType: 'image/jpeg',
        size: 1024,
        s3Key: 'test/feedback-attachment-123.jpg',
        s3Bucket: 'test-bucket',
        s3Region: 'us-east-1',
        uploadedById: testUser.id,
        category: 'feedback'
      }
    });
  });

  afterAll(async () => {
    await TestUtils.cleanDatabase();
  });

  beforeEach(async () => {
    // Clean up feedback data before each test
    await prisma.feedbackFile.deleteMany({});
    await prisma.feedback.deleteMany({});
  });

  describe('createFeedback', () => {
    it('should create basic feedback successfully', async () => {
      const feedbackData = {
        type: 'general',
        title: 'Test Feedback',
        content: 'This is a test feedback',
        category: 'general'
      };

      const feedback = await FeedbackService.createFeedback(feedbackData, testUser.id);

      expect(feedback).toBeDefined();
      expect(feedback.type).toBe('general');
      expect(feedback.title).toBe('Test Feedback');
      expect(feedback.content).toBe('This is a test feedback');
      expect(feedback.status).toBe('pending');
      expect(feedback.priority).toBe('normal');
      expect(feedback.userId).toBe(testUser.id);
      expect(feedback.user).toBeDefined();
      expect(feedback.user.id).toBe(testUser.id);
    });

    it('should create feedback with file attachments', async () => {
      const feedbackData = {
        content: 'Feedback with attachments',
        type: 'bug'
      };

      const feedback = await FeedbackService.createFeedback(
        feedbackData,
        testUser.id,
        [testFile.id]
      );

      expect(feedback).toBeDefined();
      expect(feedback.files).toBeDefined();
      expect(feedback.files.length).toBe(1);
      expect(feedback.files[0].file.id).toBe(testFile.id);
    });

    it('should create feedback related to tutor', async () => {
      const feedbackData = {
        content: 'Feedback about tutor',
        type: 'tutor_feedback',
        relatedTutorId: testTutor.id
      };

      const feedback = await FeedbackService.createFeedback(feedbackData, testUser.id);

      expect(feedback).toBeDefined();
      expect(feedback.relatedTutorId).toBe(testTutor.id);
      expect(feedback.relatedTutor).toBeDefined();
      expect(feedback.relatedTutor.id).toBe(testTutor.id);
    });

    it('should create feedback related to appointment', async () => {
      const feedbackData = {
        content: 'Feedback about appointment',
        type: 'appointment_feedback',
        relatedAppointmentId: testAppointment.id
      };

      const feedback = await FeedbackService.createFeedback(feedbackData, testUser.id);

      expect(feedback).toBeDefined();
      expect(feedback.relatedAppointmentId).toBe(testAppointment.id);
      expect(feedback.relatedAppointment).toBeDefined();
      expect(feedback.relatedAppointment.id).toBe(testAppointment.id);
    });

    it('should throw error for non-existent tutor', async () => {
      const feedbackData = {
        content: 'Feedback about non-existent tutor',
        relatedTutorId: 'non-existent-id'
      };

      await expect(
        FeedbackService.createFeedback(feedbackData, testUser.id)
      ).rejects.toThrow('Related tutor not found');
    });

    it('should throw error for non-existent appointment', async () => {
      const feedbackData = {
        content: 'Feedback about non-existent appointment',
        relatedAppointmentId: 'non-existent-id'
      };

      await expect(
        FeedbackService.createFeedback(feedbackData, testUser.id)
      ).rejects.toThrow('Related appointment not found');
    });

    it('should throw error for unauthorized appointment access', async () => {
      // Create another user
      const otherUser = await TestUtils.createTestUser('<EMAIL>', 'Other User');

      const feedbackData = {
        content: 'Feedback about unauthorized appointment',
        relatedAppointmentId: testAppointment.id
      };

      await expect(
        FeedbackService.createFeedback(feedbackData, otherUser.id)
      ).rejects.toThrow('You do not have access to this appointment');

      // Cleanup
      await prisma.user.delete({ where: { id: otherUser.id } });
    });

    it('should throw error for non-owned files', async () => {
      // Create file owned by admin
      const adminFile = await prisma.file.create({
        data: {
          originalName: 'admin-file.jpg',
          fileName: 'admin-file-123.jpg',
          mimeType: 'image/jpeg',
          size: 1024,
          s3Key: 'test/admin-file-123.jpg',
          s3Bucket: 'test-bucket',
          s3Region: 'us-east-1',
          uploadedById: testAdmin.id,
          category: 'general'
        }
      });

      const feedbackData = {
        content: 'Feedback with unauthorized file'
      };

      await expect(
        FeedbackService.createFeedback(feedbackData, testUser.id, [adminFile.id])
      ).rejects.toThrow('Some files not found or not owned by user');

      // Cleanup
      await prisma.file.delete({ where: { id: adminFile.id } });
    });
  });

  describe('getFeedbackById', () => {
    it('should get feedback by ID successfully', async () => {
      // Create feedback first
      const createdFeedback = await FeedbackService.createFeedback(
        { content: 'Test feedback for retrieval' },
        testUser.id
      );

      const feedback = await FeedbackService.getFeedbackById(createdFeedback.id, testUser.id);

      expect(feedback).toBeDefined();
      expect(feedback.id).toBe(createdFeedback.id);
      expect(feedback.content).toBe('Test feedback for retrieval');
      expect(feedback.user).toBeDefined();
    });

    it('should allow admin to access any feedback', async () => {
      // Create feedback as regular user
      const createdFeedback = await FeedbackService.createFeedback(
        { content: 'User feedback' },
        testUser.id
      );

      // Admin should be able to access it
      const feedback = await FeedbackService.getFeedbackById(createdFeedback.id, testAdmin.id);

      expect(feedback).toBeDefined();
      expect(feedback.id).toBe(createdFeedback.id);
    });

    it('should deny access to other users feedback', async () => {
      // Create another user
      const otherUser = await TestUtils.createTestUser('<EMAIL>', 'Other User 2');

      // Create feedback as test user
      const createdFeedback = await FeedbackService.createFeedback(
        { content: 'Private feedback' },
        testUser.id
      );

      // Other user should not be able to access it
      await expect(
        FeedbackService.getFeedbackById(createdFeedback.id, otherUser.id)
      ).rejects.toThrow('Access denied');

      // Cleanup
      await prisma.user.delete({ where: { id: otherUser.id } });
    });

    it('should throw error for non-existent feedback', async () => {
      await expect(
        FeedbackService.getFeedbackById('non-existent-id', testUser.id)
      ).rejects.toThrow('Feedback not found');
    });
  });

  describe('getFeedbackList', () => {
    beforeEach(async () => {
      // Create some test feedback
      await FeedbackService.createFeedback(
        { content: 'First feedback', type: 'general', status: 'pending' },
        testUser.id
      );
      await FeedbackService.createFeedback(
        { content: 'Second feedback', type: 'bug', status: 'pending' },
        testUser.id
      );
      await FeedbackService.createFeedback(
        { content: 'Admin feedback', type: 'suggestion' },
        testAdmin.id
      );
    });

    it('should get paginated feedback list', async () => {
      const result = await FeedbackService.getFeedbackList({
        page: 1,
        limit: 2
      });

      expect(result).toBeDefined();
      expect(result.items).toHaveLength(2);
      expect(result.total).toBe(3);
      expect(result.page).toBe(1);
      expect(result.limit).toBe(2);
      expect(result.totalPages).toBe(2);
    });

    it('should filter feedback by user', async () => {
      const result = await FeedbackService.getFeedbackList({
        userId: testUser.id
      });

      expect(result.items).toHaveLength(2);
      expect(result.items.every(item => item.userId === testUser.id)).toBe(true);
    });

    it('should filter feedback by type', async () => {
      const result = await FeedbackService.getFeedbackList({
        type: 'bug'
      });

      expect(result.items).toHaveLength(1);
      expect(result.items[0].type).toBe('bug');
    });

    it('should search feedback by content', async () => {
      const result = await FeedbackService.getFeedbackList({
        search: 'First'
      });

      expect(result.items).toHaveLength(1);
      expect(result.items[0].content).toContain('First');
    });
  });

  describe('updateFeedback', () => {
    let testFeedback: any;

    beforeEach(async () => {
      testFeedback = await FeedbackService.createFeedback(
        { content: 'Feedback to update' },
        testUser.id
      );
    });

    it('should update feedback status by admin', async () => {
      const updatedFeedback = await FeedbackService.updateFeedback(
        testFeedback.id,
        { status: 'in_progress', priority: 'high' },
        testAdmin.id
      );

      expect(updatedFeedback.status).toBe('in_progress');
      expect(updatedFeedback.priority).toBe('high');
    });

    it('should set resolvedAt when status changes to resolved', async () => {
      const updatedFeedback = await FeedbackService.updateFeedback(
        testFeedback.id,
        { status: 'resolved' },
        testAdmin.id
      );

      expect(updatedFeedback.status).toBe('resolved');
      expect(updatedFeedback.resolvedAt).toBeDefined();
    });

    it('should assign feedback to admin', async () => {
      const updatedFeedback = await FeedbackService.updateFeedback(
        testFeedback.id,
        { assignedToId: testAdmin.id },
        testAdmin.id
      );

      expect(updatedFeedback.assignedToId).toBe(testAdmin.id);
      expect(updatedFeedback.assignedTo).toBeDefined();
      expect(updatedFeedback.assignedTo.id).toBe(testAdmin.id);
    });

    it('should throw error for non-admin user', async () => {
      await expect(
        FeedbackService.updateFeedback(
          testFeedback.id,
          { status: 'resolved' },
          testUser.id
        )
      ).rejects.toThrow('Admin access required');
    });

    it('should throw error for non-existent feedback', async () => {
      await expect(
        FeedbackService.updateFeedback(
          'non-existent-id',
          { status: 'resolved' },
          testAdmin.id
        )
      ).rejects.toThrow('Feedback not found');
    });

    it('should throw error when assigning to non-admin', async () => {
      await expect(
        FeedbackService.updateFeedback(
          testFeedback.id,
          { assignedToId: testUser.id },
          testAdmin.id
        )
      ).rejects.toThrow('Assignee must be an admin user');
    });
  });

  describe('deleteFeedback', () => {
    it('should allow user to delete their own pending feedback', async () => {
      const feedback = await FeedbackService.createFeedback(
        { content: 'Feedback to delete' },
        testUser.id
      );

      const result = await FeedbackService.deleteFeedback(feedback.id, testUser.id);

      expect(result.success).toBe(true);

      // Verify feedback is deleted
      await expect(
        FeedbackService.getFeedbackById(feedback.id, testUser.id)
      ).rejects.toThrow('Feedback not found');
    });

    it('should allow admin to delete any feedback', async () => {
      const feedback = await FeedbackService.createFeedback(
        { content: 'Feedback to delete by admin' },
        testUser.id
      );

      const result = await FeedbackService.deleteFeedback(feedback.id, testAdmin.id);

      expect(result.success).toBe(true);
    });

    it('should prevent user from deleting processed feedback', async () => {
      const feedback = await FeedbackService.createFeedback(
        { content: 'Processed feedback' },
        testUser.id
      );

      // Update status to processed
      await FeedbackService.updateFeedback(
        feedback.id,
        { status: 'in_progress' },
        testAdmin.id
      );

      await expect(
        FeedbackService.deleteFeedback(feedback.id, testUser.id)
      ).rejects.toThrow('Cannot delete this feedback');
    });

    it('should prevent user from deleting others feedback', async () => {
      const otherUser = await TestUtils.createTestUser('<EMAIL>', 'Other User 3');

      const feedback = await FeedbackService.createFeedback(
        { content: 'Other user feedback' },
        testUser.id
      );

      await expect(
        FeedbackService.deleteFeedback(feedback.id, otherUser.id)
      ).rejects.toThrow('Cannot delete this feedback');

      // Cleanup
      await prisma.user.delete({ where: { id: otherUser.id } });
    });
  });

  describe('attachFileToFeedback', () => {
    let testFeedback: any;

    beforeEach(async () => {
      testFeedback = await FeedbackService.createFeedback(
        { content: 'Feedback for file attachment' },
        testUser.id
      );
    });

    it('should attach file to feedback successfully', async () => {
      const result = await FeedbackService.attachFileToFeedback(
        {
          feedbackId: testFeedback.id,
          fileId: testFile.id,
          description: 'Test attachment'
        },
        testUser.id
      );

      expect(result).toBeDefined();
      expect(result.feedbackId).toBe(testFeedback.id);
      expect(result.fileId).toBe(testFile.id);
      expect(result.description).toBe('Test attachment');
      expect(result.file).toBeDefined();
    });

    it('should prevent duplicate file attachments', async () => {
      // Attach file first time
      await FeedbackService.attachFileToFeedback(
        {
          feedbackId: testFeedback.id,
          fileId: testFile.id
        },
        testUser.id
      );

      // Try to attach same file again
      await expect(
        FeedbackService.attachFileToFeedback(
          {
            feedbackId: testFeedback.id,
            fileId: testFile.id
          },
          testUser.id
        )
      ).rejects.toThrow('File already attached to this feedback');
    });

    it('should allow admin to attach any file', async () => {
      const result = await FeedbackService.attachFileToFeedback(
        {
          feedbackId: testFeedback.id,
          fileId: testFile.id
        },
        testAdmin.id
      );

      expect(result).toBeDefined();
    });
  });

  describe('removeFileFromFeedback', () => {
    let testFeedback: any;

    beforeEach(async () => {
      testFeedback = await FeedbackService.createFeedback(
        { content: 'Feedback with file' },
        testUser.id,
        [testFile.id]
      );
    });

    it('should remove file from feedback successfully', async () => {
      const result = await FeedbackService.removeFileFromFeedback(
        testFeedback.id,
        testFile.id,
        testUser.id
      );

      expect(result.success).toBe(true);
    });

    it('should throw error for non-existent attachment', async () => {
      // Create another file
      const anotherFile = await prisma.file.create({
        data: {
          originalName: 'another-file.jpg',
          fileName: 'another-file-123.jpg',
          mimeType: 'image/jpeg',
          size: 1024,
          s3Key: 'test/another-file-123.jpg',
          s3Bucket: 'test-bucket',
          s3Region: 'us-east-1',
          uploadedById: testUser.id,
          category: 'general'
        }
      });

      await expect(
        FeedbackService.removeFileFromFeedback(
          testFeedback.id,
          anotherFile.id,
          testUser.id
        )
      ).rejects.toThrow('File attachment not found');

      // Cleanup
      await prisma.file.delete({ where: { id: anotherFile.id } });
    });
  });

  describe('getFeedbackStats', () => {
    beforeEach(async () => {
      // Create various feedback for stats
      await FeedbackService.createFeedback(
        { content: 'Pending feedback', status: 'pending', type: 'general', priority: 'normal' },
        testUser.id
      );
      await FeedbackService.createFeedback(
        { content: 'High priority feedback', status: 'pending', type: 'bug', priority: 'high' },
        testUser.id
      );

      const feedback = await FeedbackService.createFeedback(
        { content: 'Resolved feedback', type: 'suggestion' },
        testUser.id
      );

      await FeedbackService.updateFeedback(
        feedback.id,
        { status: 'resolved' },
        testAdmin.id
      );
    });

    it('should get comprehensive feedback statistics', async () => {
      const stats = await FeedbackService.getFeedbackStats(testAdmin.id);

      expect(stats).toBeDefined();
      expect(stats.total).toBeGreaterThan(0);
      expect(stats.pending).toBeGreaterThan(0);
      expect(stats.resolved).toBeGreaterThan(0);
      expect(stats.highPriority).toBeGreaterThan(0);
      expect(stats.byType).toBeDefined();
      expect(stats.byCategory).toBeDefined();
      expect(Array.isArray(stats.byType)).toBe(true);
      expect(Array.isArray(stats.byCategory)).toBe(true);
    });

    it('should throw error for non-admin user', async () => {
      await expect(
        FeedbackService.getFeedbackStats(testUser.id)
      ).rejects.toThrow('Admin access required');
    });
  });
});
