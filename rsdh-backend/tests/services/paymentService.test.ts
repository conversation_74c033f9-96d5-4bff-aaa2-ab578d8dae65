import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { PaymentService } from '../../src/services/paymentService';
import { prisma } from '../../src/lib/prisma';
import { FastifyInstance } from 'fastify';

// Mock Prisma
vi.mock('../../src/lib/prisma', () => ({
  prisma: {
    payment: {
      create: vi.fn(),
      findUnique: vi.fn(),
      update: vi.fn(),
      findMany: vi.fn(),
    },
    paymentSplit: {
      create: vi.fn(),
      findMany: vi.fn(),
      findUnique: vi.fn(),
      update: vi.fn(),
    },
    appointment: {
      update: vi.fn(),
    },
    $transaction: vi.fn(),
  },
}));

describe('PaymentService', () => {
  let mockApp: FastifyInstance;

  beforeEach(() => {
    mockApp = {
      config: {
        WECHAT_PAY_APP_ID: '',
        WECHAT_PAY_MCH_ID: '',
        WECHAT_PAY_API_KEY: '',
        APP_URL: 'http://localhost:3000',
      },
      log: {
        info: vi.fn(),
        error: vi.fn(),
      },
    } as any;

    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('createPayment', () => {
    it('should create a payment record', async () => {
      const mockPayment = {
        id: 'payment-1',
        appointmentId: 'appointment-1',
        tutorId: 'tutor-1',
        studentId: 'student-1',
        amount: 100,
        currency: 'CNY',
        status: 'pending',
      };

      (prisma.payment.create as any).mockResolvedValue(mockPayment);

      const result = await PaymentService.createPayment({
        appointmentId: 'appointment-1',
        tutorId: 'tutor-1',
        studentId: 'student-1',
        amount: 100,
      });

      expect(prisma.payment.create).toHaveBeenCalledWith({
        data: {
          appointmentId: 'appointment-1',
          tutorId: 'tutor-1',
          studentId: 'student-1',
          amount: 100,
          currency: 'CNY',
          paymentMethod: 'wechat',
          status: 'pending',
        },
        include: {
          appointment: true,
          tutor: {
            include: {
              user: true,
            },
          },
          student: true,
        },
      });

      expect(result).toEqual(mockPayment);
    });
  });

  describe('calculateAppointmentPrice', () => {
    it('should return 0 for free tutors', () => {
      const tutor = { isFree: true, hourlyRate: 100, halfHourRate: 50 };
      const price = PaymentService.calculateAppointmentPrice(tutor, 60);
      expect(price).toBe(0);
    });

    it('should use half-hour rate for 30-minute appointments', () => {
      const tutor = { isFree: false, hourlyRate: 100, halfHourRate: 50 };
      const price = PaymentService.calculateAppointmentPrice(tutor, 30);
      expect(price).toBe(50);
    });

    it('should use hourly rate for longer appointments', () => {
      const tutor = { isFree: false, hourlyRate: 100, halfHourRate: 50 };
      const price = PaymentService.calculateAppointmentPrice(tutor, 60);
      expect(price).toBe(100);
    });

    it('should calculate proportional price for non-standard durations', () => {
      const tutor = { isFree: false, hourlyRate: 120, halfHourRate: 0 };
      const price = PaymentService.calculateAppointmentPrice(tutor, 45); // 0.75 hours
      expect(price).toBe(90);
    });
  });

  describe('processWeChatPayment', () => {
    it('should process mock payment when WeChat is not configured', async () => {
      const paymentService = new PaymentService(mockApp);
      const mockPayment = {
        id: 'payment-1',
        amount: 100,
        appointment: { id: 'appointment-1' },
      };

      (prisma.payment.findUnique as any).mockResolvedValue(mockPayment);
      (prisma.payment.update as any).mockResolvedValue({
        ...mockPayment,
        status: 'completed',
      });

      const result = await paymentService.processWeChatPayment('payment-1', {
        amount: 100,
        description: 'Test payment',
        outTradeNo: 'test-123',
      });

      expect(result).toBeDefined();
      expect(result?.prepayId).toContain('mock_prepay_');
      expect(result?.signType).toBe('MD5');
    });

    it('should throw error when payment not found', async () => {
      const paymentService = new PaymentService(mockApp);
      (prisma.payment.findUnique as any).mockResolvedValue(null);

      await expect(
        paymentService.processWeChatPayment('invalid-payment', {
          amount: 100,
          description: 'Test payment',
          outTradeNo: 'test-123',
        })
      ).rejects.toThrow('Payment not found');
    });
  });

  describe('completePayment', () => {
    it('should complete payment and create split', async () => {
      const mockPayment = {
        id: 'payment-1',
        appointmentId: 'appointment-1',
        tutorId: 'tutor-1',
        amount: 100,
        tutor: { id: 'tutor-1' },
      };

      // Mock environment variable
      process.env.TUTOR_COMMISSION_RATE = '0.7';

      (prisma.payment.update as any).mockResolvedValue(mockPayment);
      (prisma.paymentSplit.create as any).mockResolvedValue({
        id: 'split-1',
        tutorAmount: 70,
        platformAmount: 30,
      });
      (prisma.appointment.update as any).mockResolvedValue({});

      const result = await PaymentService.completePayment('payment-1', 'tx-123');

      expect(prisma.payment.update).toHaveBeenCalledWith({
        where: { id: 'payment-1' },
        data: {
          status: 'completed',
          transactionId: 'tx-123',
          paidAt: expect.any(Date),
        },
        include: {
          appointment: true,
          tutor: true,
        },
      });

      expect(prisma.paymentSplit.create).toHaveBeenCalledWith({
        data: {
          paymentId: 'payment-1',
          tutorId: 'tutor-1',
          tutorAmount: 70,
          platformAmount: 30,
          tutorPercentage: 0.7,
          status: 'pending',
        },
      });

      expect(prisma.appointment.update).toHaveBeenCalledWith({
        where: { id: 'appointment-1' },
        data: {
          paymentStatus: 'paid',
          confirmationStatus: 'confirmed',
        },
      });
    });
  });

  describe('processPendingSplits', () => {
    it('should process splits for completed appointments after 48 hours', async () => {
      const mockSplits = [
        {
          id: 'split-1',
          tutorAmount: 70,
          tutor: { user: { name: 'Test Tutor' } },
          payment: {
            appointment: {
              status: 'completed',
              endTime: new Date(Date.now() - 49 * 60 * 60 * 1000), // 49 hours ago
            },
          },
        },
      ];

      (prisma.paymentSplit.findMany as any).mockResolvedValue(mockSplits);
      (prisma.paymentSplit.findUnique as any).mockResolvedValue(mockSplits[0]);
      (prisma.paymentSplit.update as any).mockResolvedValue({});

      await PaymentService.processPendingSplits();

      expect(prisma.paymentSplit.findMany).toHaveBeenCalledWith({
        where: {
          status: 'pending',
          payment: {
            appointment: {
              status: 'completed',
              endTime: {
                lte: expect.any(Date),
              },
            },
          },
        },
        include: {
          payment: {
            include: {
              appointment: true,
            },
          },
          tutor: {
            include: {
              user: true,
            },
          },
        },
      });

      expect(prisma.paymentSplit.update).toHaveBeenCalledWith({
        where: { id: 'split-1' },
        data: {
          status: 'completed',
          transferredAt: expect.any(Date),
          wechatTransferId: expect.stringContaining('mock_transfer_'),
        },
      });
    });
  });
});
